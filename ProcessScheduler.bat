@echo off
title Process Scheduler Simulator
cd /d "%~dp0"

echo Starting Process Scheduler Simulator...
echo.

REM Try different Java launch methods
echo [Method 1] Trying with JavaFX modules...
java --module-path . --add-modules javafx.controls,javafx.fxml -jar target/process-scheduler-simulator.jar
if %errorlevel% equ 0 goto success

echo.
echo [Method 2] Trying with classpath...
java -cp target/process-scheduler-simulator.jar org.example.scheduler.MainApp
if %errorlevel% equ 0 goto success

echo.
echo [Method 3] Trying with system JavaFX...
java -Djava.library.path=. -jar target/process-scheduler-simulator.jar
if %errorlevel% equ 0 goto success

echo.
echo [Method 4] Trying Maven JavaFX plugin...
mvn clean javafx:run
if %errorlevel% equ 0 goto success

echo.
echo ERROR: All launch methods failed!
echo.
echo Possible solutions:
echo 1. Install Java 11+ with JavaFX support
echo 2. Download OpenJFX and set JAVAFX_HOME
echo 3. Use Oracle JDK instead of OpenJDK
echo.
pause
exit /b 1

:success
echo.
echo Application started successfully!
exit /b 0
