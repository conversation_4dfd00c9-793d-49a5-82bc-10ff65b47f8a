@echo off
title 进程调度模拟器启动器
cd /d "%~dp0"

echo ========================================
echo    单处理器进程调度模拟器启动器
echo ========================================
echo.

echo [1] 使用 Maven 运行 (推荐)
echo [2] 运行 Windows EXE 文件
echo [3] 运行 JAR 包
echo [4] 重新构建项目
echo [5] 退出
echo.

set /p choice=请选择运行方式 (1-5): 

if "%choice%"=="1" goto maven_run
if "%choice%"=="2" goto exe_run
if "%choice%"=="3" goto jar_run
if "%choice%"=="4" goto rebuild
if "%choice%"=="5" goto exit

echo 无效选择，请重新运行脚本。
pause
goto exit

:maven_run
echo.
echo 正在使用 Maven 启动应用程序...
echo 这是最稳定的运行方式，会自动处理所有依赖。
echo.
mvn clean javafx:run
goto end

:exe_run
echo.
echo 正在启动 Windows EXE 文件...
echo 如果遇到问题，请尝试使用 Maven 运行方式。
echo.
if exist "target\ProcessScheduler-Fixed.exe" (
    start "" "target\ProcessScheduler-Fixed.exe"
    echo 应用程序已启动！
) else (
    echo 错误：找不到 ProcessScheduler-Fixed.exe 文件
    echo 请先运行选项 4 重新构建项目。
)
goto end

:jar_run
echo.
echo 正在运行 JAR 包...
echo 注意：可能需要正确配置的 JavaFX 环境。
echo.
if exist "target\process-scheduler-simulator.jar" (
    java -jar target\process-scheduler-simulator.jar
) else (
    echo 错误：找不到 process-scheduler-simulator.jar 文件
    echo 请先运行选项 4 重新构建项目。
)
goto end

:rebuild
echo.
echo 正在重新构建项目...
echo 这将生成新的 JAR 包和 EXE 文件。
echo.
mvn clean package -DskipTests
if %errorlevel% equ 0 (
    echo.
    echo 构建成功！生成的文件：
    echo - target\ProcessScheduler-Fixed.exe (Windows 可执行文件)
    echo - target\process-scheduler-simulator.jar (完整 JAR 包)
    echo.
) else (
    echo.
    echo 构建失败，请检查错误信息。
)
goto end

:end
echo.
echo 按任意键继续...
pause >nul

:exit
