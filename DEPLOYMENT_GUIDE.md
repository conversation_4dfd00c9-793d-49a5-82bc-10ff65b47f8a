# 进程调度模拟器部署指南

## 生成的文件说明

本项目已成功生成以下可执行文件：

### 1. 可执行JAR包
- **文件位置**: `target/process-scheduler-simulator.jar`
- **文件大小**: 约60MB
- **包含内容**: 应用程序代码 + 所有依赖库（包括JavaFX）

### 2. 启动脚本
- **Windows**: `run-scheduler.bat`
- **Linux/Mac**: `run-scheduler.sh`

## 运行方式

### 推荐方式：使用Maven（开发环境）

```bash
# 清理并运行
mvn clean javafx:run
```

**优点**：
- 自动处理JavaFX模块依赖
- 无需额外配置
- 适合开发和测试

### 方式二：直接运行JAR包

```bash
# 基本运行（可能遇到JavaFX运行时问题）
java -jar target/process-scheduler-simulator.jar

# 如果遇到JavaFX问题，使用以下命令：
java --module-path /path/to/javafx-sdk/lib --add-modules javafx.controls,javafx.fxml -jar target/process-scheduler-simulator.jar
```

### 方式三：使用启动脚本

```bash
# Windows
.\run-scheduler.bat

# Linux/Mac
chmod +x run-scheduler.sh
./run-scheduler.sh
```

## 部署包内容

完整的部署包应包含以下文件：

```
os-project/
├── target/
│   └── process-scheduler-simulator.jar    # 主要可执行文件
├── run-scheduler.bat                       # Windows启动脚本
├── run-scheduler.sh                        # Linux/Mac启动脚本
├── README.md                               # 项目说明文档
├── DEPLOYMENT_GUIDE.md                     # 本部署指南
└── pom.xml                                 # Maven配置文件（可选）
```

## 系统要求

### 最低要求
- **Java版本**: Java 11 或更高版本
- **内存**: 至少512MB可用内存
- **存储**: 至少100MB可用磁盘空间

### 推荐配置
- **Java版本**: Java 17 或 Java 21
- **内存**: 1GB或更多可用内存
- **显示器**: 分辨率至少1024x768

## 故障排除

### 常见问题1：JavaFX运行时错误

**错误信息**: "缺少 JavaFX 运行时组件"

**解决方案**:
1. 使用包含JavaFX的JDK版本（如Oracle JDK或Azul Zulu FX）
2. 或下载OpenJFX并设置模块路径
3. 或使用Maven运行：`mvn javafx:run`

### 常见问题2：Java版本不兼容

**错误信息**: "UnsupportedClassVersionError"

**解决方案**:
- 确保Java版本为11或更高
- 检查JAVA_HOME环境变量设置

### 常见问题3：内存不足

**错误信息**: "OutOfMemoryError"

**解决方案**:
```bash
java -Xmx1g -jar target/process-scheduler-simulator.jar
```

## 功能验证

启动应用程序后，应该能看到：

1. **主界面**: 包含算法选择下拉框、控制按钮等
2. **进程显示区域**: 运行进程（红色）和就绪队列（蓝色）
3. **PCB状态表**: 显示所有进程控制块信息
4. **日志区域**: 显示"PCB区域和空闲队列初始化完毕"等信息

## 性能说明

- **启动时间**: 通常5-15秒
- **内存占用**: 约100-200MB
- **CPU占用**: 正常运行时很低，自动模式下会有周期性计算

## 技术支持

如果遇到问题，请检查：
1. Java版本是否正确
2. 是否有足够的内存和磁盘空间
3. 防火墙或安全软件是否阻止了Java程序运行

## 更新说明

- 版本: 1.0-SNAPSHOT
- 构建时间: 2025-05-24
- 支持的调度算法: RR, PRIORITY, SPF, SRTF
- 最大并发进程数: 10
