@echo off
title EXE Test Script
color 0E

echo ========================================
echo   ProcessScheduler.exe Test Script
echo ========================================
echo.

echo [Step 1] Checking Java environment...
java -version
if %errorlevel% neq 0 (
    echo WARNING: Java not found in PATH, but EXE should work without it
) else (
    echo Java found successfully
)
echo.

echo [Step 2] Checking EXE file...
if exist "target\ProcessScheduler.exe" (
    echo ✓ ProcessScheduler.exe found
    echo File size: 
    dir "target\ProcessScheduler.exe" | find "ProcessScheduler.exe"
) else (
    echo ✗ ProcessScheduler.exe NOT found!
    echo Please run 'mvn clean package' first
    pause
    exit /b 1
)
echo.

echo [Step 3] Checking file permissions...
icacls "target\ProcessScheduler.exe" | find "Everyone"
echo.

echo [Step 4] Testing EXE execution...
echo Starting ProcessScheduler.exe...
echo If the application doesn't start, check for:
echo - Antivirus blocking
echo - Windows Defender SmartScreen
echo - Missing Visual C++ Redistributables
echo.

echo Press any key to start the application...
pause >nul

echo Starting application...
cd target
start "" "ProcessScheduler.exe"

echo.
echo Application launch command executed.
echo If you don't see the application window:
echo 1. Check Task Manager for ProcessScheduler.exe process
echo 2. Check Windows Event Viewer for errors
echo 3. Try running as Administrator
echo 4. Temporarily disable antivirus
echo.

echo [Step 5] Checking if process is running...
timeout /t 5 >nul
tasklist | find "ProcessScheduler.exe"
if %errorlevel% equ 0 (
    echo ✓ ProcessScheduler.exe is running!
) else (
    echo ✗ ProcessScheduler.exe is not running
    echo This might indicate a startup error
)

echo.
echo Test completed. Check the results above.
pause
