@echo off
title Process Scheduler Simulator Launcher
color 0A

echo.
echo ========================================
echo   Process Scheduler Simulator Launcher
echo ========================================
echo.

echo [1] Run EXE file (Recommended)
echo [2] Run JAR file with Java
echo [3] Run with Maven
echo [4] Exit
echo.

set /p choice="Please select an option (1-4): "

if "%choice%"=="1" goto run_exe
if "%choice%"=="2" goto run_jar
if "%choice%"=="3" goto run_maven
if "%choice%"=="4" goto exit
goto invalid

:run_exe
echo.
echo Starting EXE file...
if exist "target\ProcessScheduler.exe" (
    echo Found ProcessScheduler.exe
    echo Starting application...
    start "" "target\ProcessScheduler.exe"
    echo Application started successfully!
) else (
    echo ERROR: ProcessScheduler.exe not found!
    echo Please run 'mvn clean package' first to generate the EXE file.
)
goto end

:run_jar
echo.
echo Starting JAR file...
if exist "target\process-scheduler-simulator.jar" (
    echo Found JAR file
    echo Starting with Java...
    java -jar target\process-scheduler-simulator.jar
) else (
    echo ERROR: JAR file not found!
    echo Please run 'mvn clean package' first.
)
goto end

:run_maven
echo.
echo Starting with Maven...
echo This may take a moment...
mvn clean javafx:run
goto end

:invalid
echo.
echo Invalid option! Please select 1-4.
pause
goto start

:exit
echo.
echo Goodbye!
goto end

:end
echo.
pause
