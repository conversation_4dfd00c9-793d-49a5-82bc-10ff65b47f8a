#!/bin/bash

echo "启动进程调度模拟器..."
echo

# 检查Java版本
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java运行环境，请确保已安装Java 11或更高版本"
    exit 1
fi

# 显示Java版本
echo "Java版本信息:"
java -version

echo
echo "正在启动应用程序..."

# 启动应用程序
java --module-path . --add-modules javafx.controls,javafx.fxml,javafx.web,javafx.swing,javafx.media -jar target/process-scheduler-simulator.jar

if [ $? -ne 0 ]; then
    echo
    echo "应用程序启动失败"
    echo "请检查Java版本是否为11或更高版本"
    read -p "按任意键继续..."
fi
