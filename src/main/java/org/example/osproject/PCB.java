package org.example.osproject;

/**
 * Process Control Block (PCB) class
 * Represents a process in the system
 */
public class PCB {
    // Process states
    public static final int READY = 0;
    public static final int RUNNING = 1;
    public static final int TERMINATED = 2;
    
    private int name;        // Process identifier
    private int status;      // Process status (READY, RUNNING, TERMINATED)
    private int priority;    // Process priority (higher number means higher priority)
    private int remainingTime; // Remaining execution time (in time slices)
    private int next;        // Index of the next PCB in the queue
    
    /**
     * Constructor for PCB
     * @param name Process identifier
     * @param priority Process priority
     * @param remainingTime Remaining execution time
     */
    public PCB(int name, int priority, int remainingTime) {
        this.name = name;
        this.status = READY;
        this.priority = priority;
        this.remainingTime = remainingTime;
        this.next = -1;  // -1 indicates no next process
    }
    
    /**
     * Default constructor
     */
    public PCB() {
        this.name = 0;
        this.status = READY;
        this.priority = 0;
        this.remainingTime = 0;
        this.next = -1;
    }
    
    // Getters and setters
    public int getName() {
        return name;
    }
    
    public void setName(int name) {
        this.name = name;
    }
    
    public int getStatus() {
        return status;
    }
    
    public void setStatus(int status) {
        this.status = status;
    }
    
    public int getPriority() {
        return priority;
    }
    
    public void setPriority(int priority) {
        this.priority = priority;
    }
    
    public int getRemainingTime() {
        return remainingTime;
    }
    
    public void setRemainingTime(int remainingTime) {
        this.remainingTime = remainingTime;
    }
    
    public int getNext() {
        return next;
    }
    
    public void setNext(int next) {
        this.next = next;
    }
    
    /**
     * Decrement remaining time by 1
     * @return true if process is completed, false otherwise
     */
    public boolean decrementTime() {
        this.remainingTime--;
        return this.remainingTime <= 0;
    }
    
    /**
     * Decrement priority by 1
     */
    public void decrementPriority() {
        this.priority--;
        if (this.priority < 0) {
            this.priority = 0;
        }
    }
    
    @Override
    public String toString() {
        String statusStr;
        switch (status) {
            case READY: statusStr = "READY"; break;
            case RUNNING: statusStr = "RUNNING"; break;
            case TERMINATED: statusStr = "TERMINATED"; break;
            default: statusStr = "UNKNOWN";
        }
        
        return "Process ID: " + name + 
               ", Status: " + statusStr + 
               ", Priority: " + priority + 
               ", Remaining Time: " + remainingTime;
    }
}
