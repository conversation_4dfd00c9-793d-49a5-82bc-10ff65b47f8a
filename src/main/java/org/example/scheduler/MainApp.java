package org.example.scheduler;

import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.io.IOException;
import java.util.Objects;

public class MainApp extends Application {

    @Override
    public void start(Stage primaryStage) throws IOException {
        // 确保从资源中正确加载FXML文件
        FXMLLoader loader = new FXMLLoader(Objects.requireNonNull(getClass().getResource("/org/example/osproject/scheduler-view.fxml")));
        VBox root = loader.load(); // 假设根元素是VBox，与FXML一致

        Scene scene = new Scene(root, 950, 750); // 调整大小以获得更好的布局
        primaryStage.setTitle("单处理器进程调度模拟器 (JavaFX)");
        primaryStage.setScene(scene);

        SchedulerController controller = loader.getController();
        controller.setStage(primaryStage); // 将舞台传递给控制器以便显示警告对话框

        primaryStage.setOnCloseRequest(event -> {
            // 确保在窗口关闭时停止自动运行线程
            if (controller != null) {
                controller.stopAutoRun(); // 调用方法停止自动运行（如果活动）
            }
        });

        primaryStage.show();
    }

    public static void main(String[] args) {
        launch(args);
    }
}