package org.example.scheduler;


import javafx.beans.property.IntegerProperty;
import javafx.beans.property.SimpleIntegerProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;

/*
 *  进程控制块(PCB)结构
 */
public class PCB {
    public int name;//进程标识符
    public ProcessState status;//进程状态
    public int pri;//进程优先数
    public int time;//剩余运行时间
    public int next;//下一个进程控制块(PCB)的位置

    public int initialBurstTime;//分配初始运行时间
    public int arrivalTime;//进程到达时间

    private transient IntegerProperty nameProperty;
    private transient StringProperty statusProperty;
    private transient IntegerProperty priProperty;
    private transient IntegerProperty timeProperty;
    private transient IntegerProperty nextProperty;
    private transient IntegerProperty initialBurstTimeProperty;
    private transient IntegerProperty arrivalTimeProperty;

    public PCB() {
        this.name = -1;
        this.status = ProcessState.READY;
        this.pri = 0;
        this.time = 0;
        this.next =-1;//链表指针初始化
        this.initialBurstTime = 0;
        this.arrivalTime = 0;

        initProperties();//调用属性初始化方法
    }

    /*
    重置PCB状态，通常在返回到空闲链表时调用
     */
    public void reset(){
        this.name =-1;
        this.status = ProcessState.READY;
        this.pri = 0;
        this.time = 0;
        this.next =-1;
        this.initialBurstTime = 0;
        this.arrivalTime = 0;
        updateProperties();//更新相关属性
    }
    public void initProperties(){
        this.nameProperty = new SimpleIntegerProperty(this.name);
        this.statusProperty = new SimpleStringProperty(this.status.getDescription());
        this.priProperty = new SimpleIntegerProperty(this.pri);
        this.timeProperty = new SimpleIntegerProperty(this.time);
        this.nextProperty = new SimpleIntegerProperty(this.next);
        this.initialBurstTimeProperty = new SimpleIntegerProperty(this.initialBurstTime);
        this.arrivalTimeProperty = new SimpleIntegerProperty(this.arrivalTime);
    }

    public void updateProperties(){
       if(nameProperty == null) initProperties();
       nameProperty.set(name);
       statusProperty.set(status!=null?status.getDescription():"");
       priProperty.set(pri);
       timeProperty.set(time);
       nextProperty.set(next);
       initialBurstTimeProperty.set(initialBurstTime);
       arrivalTimeProperty.set(arrivalTime);
    }

    public IntegerProperty nameProperty() {
        return nameProperty;
    }

    public StringProperty statusProperty() {
        return statusProperty;
    }

    public IntegerProperty priProperty() {
        return priProperty;
    }

    public IntegerProperty timeProperty() {
        return timeProperty;
    }

    public IntegerProperty nextProperty() {
        return nextProperty;
    }

    public IntegerProperty initialBurstTimeProperty() {
        return initialBurstTimeProperty;
    }

    public IntegerProperty arrivalTimeProperty() {
        return arrivalTimeProperty;
    }

    public void setName(int name) {
        this.name = name;
        updateProperties();
    }

    public void setStatus(ProcessState status) {
        this.status = status;
        updateProperties();
    }

    public void setPri(int pri) {
        this.pri = pri;
        updateProperties();
    }

    public void setTime(int time) {
        this.time = time;
        updateProperties();
    }

    public void setNext(int next) {
        this.next = next;
        updateProperties();

    }

    public void setInitialBurstTime(int initialBurstTime) {
        this.initialBurstTime = initialBurstTime;
        updateProperties();
    }

    public void setArrivalTime(int arrivalTime) {
        this.arrivalTime = arrivalTime;
        updateProperties();
    }

    @Override
    public String toString(){
        return String.format("ID:%d,状态:%s,优先数:%d,剩余时间:%d,初始时间:%d",
                name,status.getDescription(),pri,time,initialBurstTime);
    }
}
