package org.example.scheduler;

import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.control.*;
import javafx.scene.layout.FlowPane;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.scene.shape.Rectangle;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.stage.Stage;
import org.example.scheduler.PCB;
import org.example.scheduler.ProcessSchedulerModel;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class SchedulerController {

    // --- 原有的 FXML 注入 ---
    @FXML private ChoiceBox<String> algorithmChoiceBox;
    @FXML private Label currentTimeLabel;
    @FXML private TableView<PCB> pcbAreaTableView;
    @FXML private TableColumn<PCB, Integer> pcbNameCol;
    @FXML private TableColumn<PCB, String> pcbStatusCol;
    @FXML private TableColumn<PCB, Integer> pcbPriCol;
    @FXML private TableColumn<PCB, Integer> pcbTimeCol;
    @FXML private TableColumn<PCB, Integer> pcbInitialTimeCol;
    @FXML private TableColumn<PCB, Integer> pcbArrivalCol;
    @FXML private TableColumn<PCB, Integer> pcbNextCol;
    @FXML private TextArea logTextArea;
    @FXML private Button addInitialButton;
    @FXML private Button addRandomButton;
    @FXML private Button nextStepButton;
    @FXML private Button autoRunButton;
    @FXML private Button resetButton;

    // --- 新增的 FXML 注入 ---
    @FXML private StackPane runningProcessPane; // 用于显示运行中的进程方块
    @FXML private FlowPane readyQueueFlowPane;   // 用于显示就绪队列的进程方块
    @FXML private Slider speedSlider;            // 速度控制滑块
    @FXML private Label speedLabel;              // 显示当前速度的标签
    @FXML private ProgressBar processProgressBar; // 进程完成进度条
    @FXML private Label progressPercentLabel;     // 进度百分比标签
    @FXML private Label timeRemainingLabel;       // 剩余时间和总时间标签

    private ProcessSchedulerModel model;
    private Stage stage;

    private volatile boolean autoRunning = false;
    private int autoRunStepsRemaining = 0;
    private static final int AUTO_RUN_TOTAL_STEPS = 20;
    // private static final long DEFAULT_AUTO_RUN_DELAY_MS = 500; // 改为可变
    private long currentAutoRunDelayMs = 500; // 默认500ms，会被滑块改变

    // --- 颜色定义 ---
    private final Color RUNNING_BG_COLOR = Color.rgb(220, 66, 66, 0.9); // 红色
    private final Color READY_BG_COLOR = Color.rgb(66, 134, 220, 0.9);  // 蓝色
    private final Color IDLE_BG_COLOR = Color.rgb(224, 224, 224, 0.8);  // 浅灰色 (白色)
    private final Color TEXT_COLOR_ON_DARK = Color.WHITE;
    private final Color TEXT_COLOR_ON_LIGHT = Color.BLACK;
    private final double BLOCK_WIDTH = 70;
    private final double BLOCK_HEIGHT = 55;

    public void setStage(Stage stage) {
        this.stage = stage;
    }

    @FXML
    public void initialize() {
        model = new ProcessSchedulerModel();
        algorithmChoiceBox.setItems(FXCollections.observableArrayList(
                "RR", "PRIORITY", "SPF", "SRTF"
        ));
        algorithmChoiceBox.setValue("RR");

        // 表格列的初始化 (与之前相同)
        pcbNameCol.setCellValueFactory(cellData -> cellData.getValue().nameProperty().asObject());
        pcbStatusCol.setCellValueFactory(cellData -> cellData.getValue().statusProperty());
        pcbPriCol.setCellValueFactory(cellData -> cellData.getValue().priProperty().asObject());
        pcbTimeCol.setCellValueFactory(cellData -> cellData.getValue().timeProperty().asObject());
        pcbInitialTimeCol.setCellValueFactory(cellData -> cellData.getValue().initialBurstTimeProperty().asObject());
        pcbArrivalCol.setCellValueFactory(cellData -> cellData.getValue().arrivalTimeProperty().asObject());
        pcbNextCol.setCellValueFactory(cellData -> cellData.getValue().nextProperty().asObject());

        // 初始化速度滑块
        speedSlider.setMin(50);  // 最小延迟 50ms
        speedSlider.setMax(2000); // 最大延迟 2000ms (2s)
        speedSlider.setValue(currentAutoRunDelayMs);
        speedSlider.setShowTickLabels(true);
        speedSlider.setShowTickMarks(true);
        speedSlider.setMajorTickUnit(450);
        speedSlider.setMinorTickCount(4); // 每两个大刻度之间的小刻度数量
        speedSlider.setBlockIncrement(100);

        speedLabel.setText(String.format("%d ms", (int)currentAutoRunDelayMs));
        speedSlider.valueProperty().addListener((obs, oldVal, newVal) -> {
            currentAutoRunDelayMs = newVal.longValue();
            speedLabel.setText(String.format("%d ms", newVal.intValue()));
        });

        updateUI();
    }

    /**
     * 创建一个代表进程的彩色方块节点
     * @param pcb 进程控制块
     * @param bgColor 方块背景色
     * @param textColor 方块文字颜色
     * @return Node 代表进程的方块
     */
    private Node createProcessBlock(PCB pcb, Color bgColor, Color textColor) {
        StackPane blockPane = new StackPane();
        blockPane.setPrefSize(BLOCK_WIDTH, BLOCK_HEIGHT);
        blockPane.setMinSize(BLOCK_WIDTH, BLOCK_HEIGHT);

        Rectangle background = new Rectangle(BLOCK_WIDTH, BLOCK_HEIGHT);
        background.setFill(bgColor);
        background.setStroke(Color.rgb(50,50,50,0.7)); // 深色边框
        background.setArcWidth(10); // 圆角
        background.setArcHeight(10);

        VBox content = new VBox(2); // 垂直排列ID和时间
        content.setAlignment(Pos.CENTER);

        Label idLabel = new Label("ID: " + pcb.name);
        idLabel.setFont(Font.font("System", FontWeight.BOLD, 11));
        idLabel.setTextFill(textColor);

        Label timeLabel = new Label("T: " + pcb.time);
        timeLabel.setFont(Font.font("System", FontWeight.NORMAL, 10));
        timeLabel.setTextFill(textColor);

        Label prioLabel = new Label("P: " + pcb.pri);
        prioLabel.setFont(Font.font("System", FontWeight.NORMAL, 10));
        prioLabel.setTextFill(textColor);

        content.getChildren().addAll(idLabel, timeLabel, prioLabel);
        blockPane.getChildren().addAll(background, content);

        // 添加Tooltip显示详细信息
        Tooltip tooltip = new Tooltip(
                String.format("进程详情:\nID: %d\n状态: %s\n优先数: %d\n剩余时间: %d\n初始时间: %d\n到达时间: %d",
                        pcb.name, pcb.status.getChineseName(), pcb.pri, pcb.time, pcb.initialBurstTime, pcb.arrivalTime)
        );
        Tooltip.install(blockPane, tooltip);

        return blockPane;
    }

    /**
     * 创建一个表示CPU空闲的方块
     */
    private Node createIdleBlock() {
        StackPane idlePane = new StackPane();
        idlePane.setPrefSize(BLOCK_WIDTH * 1.5, BLOCK_HEIGHT); // 可以让空闲块稍大一些或不同形状
        idlePane.setMinSize(BLOCK_WIDTH * 1.5, BLOCK_HEIGHT);

        Rectangle background = new Rectangle(BLOCK_WIDTH*1.5, BLOCK_HEIGHT);
        background.setFill(IDLE_BG_COLOR);
        background.setStroke(Color.DARKGRAY);
        background.setArcWidth(10);
        background.setArcHeight(10);

        Label idleLabel = new Label("CPU 空闲");
        idleLabel.setFont(Font.font("System", FontWeight.BOLD, 12));
        idleLabel.setTextFill(TEXT_COLOR_ON_LIGHT);

        idlePane.getChildren().addAll(background, idleLabel);
        return idlePane;
    }

    private void updateUI() {
        currentTimeLabel.setText(String.valueOf(model.getCurrentTime()));

        // 1. 更新正在运行的进程方块
        runningProcessPane.getChildren().clear();
        PCB running = model.getRunningProcess();
        if (running != null) {
            Node runningBlock = createProcessBlock(running, RUNNING_BG_COLOR, TEXT_COLOR_ON_DARK);
            runningProcessPane.getChildren().add(runningBlock);

            // 更新进度条
            double progress = 1.0 - (double) running.time / running.initialBurstTime;
            processProgressBar.setProgress(progress);
            int percent = (int) (progress * 100);
            progressPercentLabel.setText(percent + "%");

            // 更新剩余时间和总时间
            timeRemainingLabel.setText(String.format("剩余时间: %d / 总时间: %d",
                    running.time, running.initialBurstTime));
        } else {
            Node idleBlock = createIdleBlock();
            runningProcessPane.getChildren().add(idleBlock);

            // 重置进度条
            processProgressBar.setProgress(0);
            progressPercentLabel.setText("0%");
            timeRemainingLabel.setText("剩余时间: -- / 总时间: --");
        }

        // 2. 更新就绪队列的进程方块
        readyQueueFlowPane.getChildren().clear();
        List<PCB> readyQueue = model.getReadyQueueContents();
        if (readyQueue != null) {
            readyQueue.forEach(pcb -> {
                Node readyBlock = createProcessBlock(pcb, READY_BG_COLOR, TEXT_COLOR_ON_DARK);
                readyQueueFlowPane.getChildren().add(readyBlock);
            });
        }

        // 3. 更新PCB区域表格 (与之前相同)
        List<PCB> pcbListForTable = Arrays.stream(model.getPcbArray())
                .peek(PCB::updateProperties)
                .collect(Collectors.toList());
        pcbAreaTableView.setItems(FXCollections.observableArrayList(pcbListForTable));

        // 4. 更新日志 (与之前相同)
        logTextArea.setText(model.getLog());
        logTextArea.setScrollTop(Double.MAX_VALUE);
    }

    @FXML
    private void handleAddInitialProcesses() {
        String selectedAlgorithm = algorithmChoiceBox.getValue();
        for (int i = 0; i < 3; i++) {
            int newPcbIdx = model.createNewProcessRaw();
            if (newPcbIdx != -1) {
                model.addProcessToReadyQueue(newPcbIdx, selectedAlgorithm);
            } else {
                showAlert("创建错误", "无法创建更多进程，PCB区域已满。");
                break;
            }
        }
        updateUI();
    }

    @FXML
    private void handleAddRandomProcess() {
        String selectedAlgorithm = algorithmChoiceBox.getValue();
        int newPcbIdx = model.createNewProcessRaw();
        if (newPcbIdx != -1) {
            model.addProcessToReadyQueue(newPcbIdx, selectedAlgorithm);
            updateUI();
        } else {
            showAlert("创建错误", "无法创建进程，PCB区域已满。");
        }
    }

    @FXML
    private void handleNextStep() {
        if (autoRunning) {
            stopAutoRun();
        }
        performSingleStep();
    }

    private void performSingleStep() {
        String selectedAlgorithm = algorithmChoiceBox.getValue();
        model.dynamicallyAddProcess(selectedAlgorithm);

        switch (selectedAlgorithm) {
            case "RR": model.stepRoundRobin(); break;
            case "PRIORITY": model.stepPriority(); break;
            case "SPF": model.stepSPF(); break;
            case "SRTF": model.stepSRTF(); break;
        }
        updateUI();
    }

    @FXML
    private void handleAutoRun() {
        if (autoRunning) {
            stopAutoRun();
        } else {
            startAutoRun();
        }
    }

    private void startAutoRun() {
        autoRunning = true;
        autoRunStepsRemaining = AUTO_RUN_TOTAL_STEPS;
        autoRunButton.setText("停止自动运行");
        disableControls(true);

        new Thread(() -> {
            while (autoRunning && autoRunStepsRemaining > 0) {
                Platform.runLater(this::performSingleStep);
                autoRunStepsRemaining--;
                try {
                    Thread.sleep(currentAutoRunDelayMs); // 使用可变的速度
                } catch (InterruptedException e) {
                    if (!autoRunning) break;
                    Thread.currentThread().interrupt();
                }
            }
            Platform.runLater(() -> {
                if (autoRunning) { // 自然结束
                    showAlert("自动运行完成", "自动运行已完成 " + AUTO_RUN_TOTAL_STEPS + " 步。");
                }
                stopAutoRun(); // 确保状态重置
            });
        }).start();
    }

    // public方法，便于MainApp在窗口关闭时调用
    public void stopAutoRun() {
        autoRunning = false; // 这会使后台线程的循环条件为false，从而退出循环
        autoRunButton.setText("自动运行 (" + AUTO_RUN_TOTAL_STEPS + "步)");
        disableControls(false);
    }

    private void disableControls(boolean disable) {
        addInitialButton.setDisable(disable);
        addRandomButton.setDisable(disable);
        nextStepButton.setDisable(disable);
        resetButton.setDisable(disable);
        algorithmChoiceBox.setDisable(disable);
        speedSlider.setDisable(disable); // 自动运行时禁用速度调节，或允许（取决于设计）
        // 当前设计为禁用，若要允许，则移除此行
    }

    @FXML
    private void handleReset() {
        if (autoRunning) {
            stopAutoRun();
        }
        model.resetSimulation();
        updateUI();
    }

    private void showAlert(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        if (stage != null) {
            alert.initOwner(stage);
        }
        alert.showAndWait();
    }
}
