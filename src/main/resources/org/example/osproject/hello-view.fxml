<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ChoiceBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<?import javafx.scene.layout.RowConstraints?>
<VBox alignment="TOP_CENTER" spacing="10.0" style="-fx-font-family: 'Microsoft YaHei', sans-serif;" xmlns="http://javafx.com/javafx/17" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.scheduler.SchedulerController">
    <padding>
        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
    </padding>
    <Label text="单处理器进程调度模拟">
        <font>
            <Font name="System Bold" size="18.0" />
        </font>
    </Label>
    <HBox alignment="CENTER_LEFT" spacing="10.0">
        <children>
            <Label text="选择调度算法:" />
            <ChoiceBox fx:id="algorithmChoiceBox" prefWidth="120.0" />
            <Button fx:id="addInitialButton" onAction="#handleAddInitialProcesses" text="添加初始进程 (3)" />
            <Button fx:id="addRandomButton" onAction="#handleAddRandomProcess" text="添加随机进程" />
            <Button fx:id="nextStepButton" onAction="#handleNextStep" text="下一步" />
            <Button fx:id="autoRunButton" onAction="#handleAutoRun" text="自动运行 (20步)" />
            <Button fx:id="resetButton" onAction="#handleReset" text="重置模拟" />
        </children>
        <VBox.margin>
            <Insets />
        </VBox.margin>
    </HBox>
    <HBox alignment="CENTER_LEFT" spacing="10.0">
        <children>
            <Label text="当前时间:" />
            <Label fx:id="currentTimeLabel" text="0" />
        </children>
    </HBox>
    <GridPane hgap="10.0" vgap="5.0">
        <columnConstraints>
            <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" percentWidth="40.0" />
            <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" percentWidth="60.0" />
        </columnConstraints>
        <rowConstraints>
            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
            <RowConstraints minHeight="10.0" prefHeight="120.0" vgrow="SOMETIMES" />
        </rowConstraints>
        <children>
            <Label text="正在运行的进程:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
            <VBox fx:id="runningProcessBox" spacing="5.0" style="-fx-border-color: lightgray; -fx-padding: 5;" GridPane.columnIndex="0" GridPane.rowIndex="1">
                <children>
                    <Label fx:id="runningProcessIdLabel" text="ID: --" />
                    <Label fx:id="runningProcessPriLabel" text="优先数: --" />
                    <Label fx:id="runningProcessTimeLabel" text="剩余时间: --" />
                </children>
            </VBox>
            <Label text="就绪队列 (头部优先):" GridPane.columnIndex="1" GridPane.rowIndex="0" />
            <ListView fx:id="readyQueueListView" prefHeight="120.0" GridPane.columnIndex="1" GridPane.rowIndex="1" />
        </children>
    </GridPane>
    <Label text="PCB区域 (所有进程控制块状态):" />
    <TableView fx:id="pcbAreaTableView" prefHeight="180.0">
        <columns>
            <TableColumn fx:id="pcbNameCol" prefWidth="75.0" text="进程名(ID)" />
            <TableColumn fx:id="pcbStatusCol" prefWidth="75.0" text="状态" />
            <TableColumn fx:id="pcbPriCol" prefWidth="75.0" text="优先数" />
            <TableColumn fx:id="pcbTimeCol" prefWidth="85.0" text="剩余时间" />
            <TableColumn fx:id="pcbInitialTimeCol" prefWidth="85.0" text="初始时间" />
            <TableColumn fx:id="pcbArrivalCol" prefWidth="75.0" text="到达时间" />
            <TableColumn fx:id="pcbNextCol" prefWidth="75.0" text="Next指针" />
        </columns>
    </TableView>
    <Label text="调度日志:" />
    <TextArea fx:id="logTextArea" editable="false" prefHeight="150.0" wrapText="true" />
</VBox>