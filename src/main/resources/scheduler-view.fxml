<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx"
      xmlns:fx="http://javafx.com/fxml"
      fx:controller="org.example.scheduler.SchedulerController"
      spacing="10" alignment="CENTER" style="-fx-padding: 15;">
    
    <HBox spacing="10" alignment="CENTER">
        <Label text="调度算法:" style="-fx-font-weight: bold;"/>
        <ChoiceBox fx:id="algorithmChoiceBox" prefWidth="100"/>
    </HBox>
    
    <HBox spacing="20" alignment="CENTER">
        <Button fx:id="addInitialButton" text="添加初始进程" onAction="#handleAddInitialProcesses"/>
        <Button fx:id="addRandomButton" text="添加随机进程" onAction="#handleAddRandomProcess"/>
        <Button fx:id="nextStepButton" text="下一步" onAction="#handleNextStep"/>
        <Button fx:id="autoRunButton" text="自动运行 (20步)" onAction="#handleAutoRun"/>
        <Button fx:id="resetButton" text="重置" onAction="#handleReset"/>
    </HBox>
    
    <HBox spacing="20" alignment="CENTER_LEFT">
        <VBox spacing="5" alignment="CENTER_LEFT" style="-fx-border-color: lightgray; -fx-border-radius: 5; -fx-padding: 10;">
            <Label text="系统时间:" style="-fx-font-weight: bold;"/>
            <Label fx:id="currentTimeLabel" text="0" style="-fx-font-size: 16;"/>
        </VBox>
        
        <VBox spacing="5" alignment="CENTER_LEFT" style="-fx-border-color: lightgray; -fx-border-radius: 5; -fx-padding: 10;">
            <Label text="运行进程:" style="-fx-font-weight: bold;"/>
            <HBox spacing="10">
                <Label fx:id="runningProcessIdLabel" text="ID: --"/>
                <Label fx:id="runningProcessPriLabel" text="优先数: --"/>
                <Label fx:id="runningProcessTimeLabel" text="剩余时间: --"/>
            </HBox>
        </VBox>
    </HBox>
    
    <HBox spacing="10" VBox.vgrow="ALWAYS">
        <VBox spacing="5" HBox.hgrow="ALWAYS" style="-fx-border-color: lightgray; -fx-border-radius: 5; -fx-padding: 10;">
            <Label text="就绪队列:" style="-fx-font-weight: bold;"/>
            <ListView fx:id="readyQueueListView" VBox.vgrow="ALWAYS"/>
        </VBox>
        
        <VBox spacing="5" HBox.hgrow="ALWAYS" style="-fx-border-color: lightgray; -fx-border-radius: 5; -fx-padding: 10;">
            <Label text="PCB区域:" style="-fx-font-weight: bold;"/>
            <TableView fx:id="pcbAreaTableView" VBox.vgrow="ALWAYS">
                <columns>
                    <TableColumn fx:id="pcbNameCol" text="ID" prefWidth="50"/>
                    <TableColumn fx:id="pcbStatusCol" text="状态" prefWidth="80"/>
                    <TableColumn fx:id="pcbPriCol" text="优先数" prefWidth="60"/>
                    <TableColumn fx:id="pcbTimeCol" text="剩余时间" prefWidth="80"/>
                    <TableColumn fx:id="pcbInitialTimeCol" text="初始时间" prefWidth="80"/>
                    <TableColumn fx:id="pcbArrivalCol" text="到达时间" prefWidth="80"/>
                    <TableColumn fx:id="pcbNextCol" text="下一个" prefWidth="60"/>
                </columns>
            </TableView>
        </VBox>
    </HBox>
    
    <VBox spacing="5" style="-fx-border-color: lightgray; -fx-border-radius: 5; -fx-padding: 10;">
        <Label text="日志:" style="-fx-font-weight: bold;"/>
        <TextArea fx:id="logTextArea" editable="false" wrapText="true" prefHeight="150"/>
    </VBox>
</VBox>
