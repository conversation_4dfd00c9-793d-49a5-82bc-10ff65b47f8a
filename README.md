# 单处理器进程调度模拟器

## 项目简介

这是一个基于JavaFX的进程调度算法可视化模拟器，实现了四种经典的进程调度算法：
- **RR (Round Robin)**: 时间片轮转调度
- **PRIORITY**: 优先数调度
- **SPF (Shortest Process First)**: 最短进程优先调度
- **SRTF (Shortest Remaining Time First)**: 最短剩余时间优先调度

## 系统要求

- Java 11 或更高版本
- 支持JavaFX的Java运行环境

## 运行方式

### 方式一：使用Maven运行（推荐）

```bash
mvn clean javafx:run
```

### 方式二：使用生成的JAR包

1. **生成JAR包**：
```bash
mvn clean package
```

2. **运行JAR包**：
```bash
# Windows
java -jar target/process-scheduler-simulator.jar

# 如果遇到JavaFX运行时问题，请使用：
java --module-path "path/to/javafx/lib" --add-modules javafx.controls,javafx.fxml -jar target/process-scheduler-simulator.jar
```

### 方式三：使用IDE运行

直接运行 `src/main/java/org/example/scheduler/MainApp.java` 文件

## 功能特性

### 调度算法
- **时间片轮转 (RR)**: 每个进程分配固定时间片，轮流执行
- **优先数调度 (PRIORITY)**: 按优先数大小调度，运行后优先数递减
- **最短进程优先 (SPF)**: 按总运行时间排序，非抢占式
- **最短剩余时间优先 (SRTF)**: 按剩余时间排序，支持抢占

### 可视化界面
- **实时进程状态显示**: 彩色方块表示运行中和就绪队列的进程
- **进度条**: 显示当前运行进程的完成进度
- **PCB区域表格**: 显示所有进程控制块的详细状态
- **调度日志**: 实时记录调度过程
- **速度控制**: 可调节自动运行的速度

### 交互功能
- 添加初始进程/随机进程
- 单步执行/自动运行
- 算法切换
- 模拟重置
- 速度调节

## 项目结构

```
src/main/java/org/example/scheduler/
├── MainApp.java              # JavaFX应用程序入口
├── PCB.java                  # 进程控制块类
├── ProcessState.java         # 进程状态枚举
├── ProcessSchedulerModel.java # 调度算法核心逻辑
└── SchedulerController.java  # UI控制器

src/main/resources/org/example/osproject/
└── scheduler-view.fxml       # JavaFX界面布局文件
```

## 技术栈

- **Java 21**: 编程语言
- **JavaFX 21**: GUI框架
- **Maven**: 项目管理和构建工具
- **FXML**: 界面布局描述

## 开发者信息

- **架构模式**: MVC (Model-View-Controller)
- **数据结构**: 数组模拟PCB区域，链表管理就绪队列
- **并发处理**: JavaFX Platform.runLater()确保线程安全
- **数据绑定**: JavaFX Property实现界面与数据的自动同步

## 故障排除

### JavaFX运行时问题

如果遇到"缺少 JavaFX 运行时组件"错误，请：

1. 确保使用支持JavaFX的JDK版本
2. 或者下载OpenJFX并设置模块路径：
```bash
java --module-path /path/to/javafx-sdk/lib --add-modules javafx.controls,javafx.fxml -jar target/process-scheduler-simulator.jar
```

### 编译问题

如果遇到编译错误，请：
1. 确保Java版本为11或更高
2. 检查Maven配置
3. 清理并重新构建：`mvn clean compile`

## 许可证

本项目仅用于教学和学习目的。
