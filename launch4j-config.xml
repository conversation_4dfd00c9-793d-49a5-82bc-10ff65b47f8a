<?xml version="1.0" encoding="UTF-8"?>
<launch4jConfig>
  <dontWrapJar>false</dontWrapJar>
  <headerType>gui</headerType>
  <jar>target/process-scheduler-simulator.jar</jar>
  <outfile>target/ProcessScheduler.exe</outfile>
  <errTitle></errTitle>
  <cmdLine></cmdLine>
  <chdir>.</chdir>
  <priority>normal</priority>
  <downloadUrl>https://www.oracle.com/java/technologies/downloads/</downloadUrl>
  <supportUrl></supportUrl>
  <stayAlive>false</stayAlive>
  <restartOnCrash>false</restartOnCrash>
  <manifest></manifest>
  <icon></icon>
  <jre>
    <path></path>
    <bundledJre64Bit>false</bundledJre64Bit>
    <bundledJreAsFallback>false</bundledJreAsFallback>
    <minVersion>11</minVersion>
    <maxVersion></maxVersion>
    <jdkPreference>preferJre</jdkPreference>
    <runtimeBits>64/32</runtimeBits>
    <opt>-Dfile.encoding=UTF-8</opt>
    <opt>-Xmx1024m</opt>
  </jre>
  <versionInfo>
    <fileVersion>*******</fileVersion>
    <txtFileVersion>1.0.0</txtFileVersion>
    <fileDescription>Process Scheduler Simulator</fileDescription>
    <copyright>2025</copyright>
    <productVersion>*******</productVersion>
    <txtProductVersion>1.0.0</txtProductVersion>
    <productName>Process Scheduler Simulator</productName>
    <companyName>Example Organization</companyName>
    <internalName>ProcessScheduler</internalName>
    <originalFilename>ProcessScheduler.exe</originalFilename>
  </versionInfo>
</launch4jConfig>
